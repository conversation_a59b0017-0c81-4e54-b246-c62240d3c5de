import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { fetchFromStorage, saveToStorage } from './storage';
import siteConstant from '../constant/siteConstant';
import apiInstance from '../Axios/axiosINstance';
import { URL } from '../constant/Url';

const UserContext = createContext();

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export const UserProvider = ({ children }) => {
  // Current active user state
  const [activeUser, setActiveUser] = useState(null);
  const [isUserLoading, setIsUserLoading] = useState(false);
  const [userSwitchTrigger, setUserSwitchTrigger] = useState(0);
  
  // Available users for switching
  const [availableUsers, setAvailableUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Initialize active user from storage
  useEffect(() => {
    const initializeUser = () => {
      // Check if there's a selected user in localStorage
      const selectedUserId = localStorage.getItem('SelectedUserId');
      const selectedUserToken = localStorage.getItem('UserToken');
      const selectedUserName = localStorage.getItem('UserName');
      const selectedUsername = localStorage.getItem('Username');
      const selectedUserImage = localStorage.getItem('UserProfileImage');
      const selectedUserStatus = localStorage.getItem('UserStatus');
      const selectedIsAdmin = localStorage.getItem('IsAdmin');

      if (selectedUserId && selectedUserToken) {
        // User has been switched, use the selected user data
        const switchedUser = {
          id: selectedUserId,
          user_id: selectedUserId,
          token: selectedUserToken,
          name: selectedUserName,
          username: selectedUsername,
          profile_image: selectedUserImage,
          user_status: selectedUserStatus,
          is_admin: selectedIsAdmin === 'true',
          isSwitchedUser: true
        };
        setActiveUser(switchedUser);
      } else {
        // No switched user, use the main logged-in user
        const mainUser = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
        if (mainUser) {
          setActiveUser({
            ...mainUser,
            isSwitchedUser: false
          });
        }
      }
    };

    initializeUser();
  }, []);

  // Fetch available users for switching
  const fetchAvailableUsers = useCallback(async () => {
    try {
      setLoadingUsers(true);
      const mainUser = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const switchUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA);
      const brandId = switchUserData?.brand_id;

      if (!mainUser || !brandId) {
        console.log('No main user or brand ID found');
        return;
      }

      const response = await apiInstance.get(URL.All_USERS, {
        headers: {
          brand: brandId,
          user: mainUser.user_id,
        },
      });

      if (response?.data?.data) {
        const userData = [response.data.data];
        setAvailableUsers(userData);
      }
    } catch (error) {
      console.error('Error fetching available users:', error);
      setAvailableUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  }, []);

  // Switch to a different user
  const switchUser = useCallback((user) => {
    console.log('Switching to user:', user);
    
    // Store selected user data in localStorage
    localStorage.setItem('SelectedUserId', user?.user_id || user?.id || '');
    localStorage.setItem('UserToken', user?.token || '');
    localStorage.setItem('Username', user?.username || '');
    localStorage.setItem('UserName', user?.name || '');
    localStorage.setItem('UserProfileImage', user?.profile_image || '');
    localStorage.setItem('UserStatus', user?.user_status || '');
    localStorage.setItem('IsAdmin', user?.is_admin || false);

    // Update active user state
    const switchedUser = {
      id: user?.user_id || user?.id,
      user_id: user?.user_id || user?.id,
      token: user?.token,
      name: user?.name,
      username: user?.username,
      profile_image: user?.profile_image,
      user_status: user?.user_status,
      is_admin: user?.is_admin,
      isSwitchedUser: true,
      ...user
    };

    setActiveUser(switchedUser);
    
    // Trigger data refresh across all components
    setUserSwitchTrigger(prev => prev + 1);
    
    console.log('User switched successfully:', switchedUser);
  }, []);

  // Switch back to main user
  const switchToMainUser = useCallback(() => {
    console.log('Switching back to main user');
    
    // Clear selected user data from localStorage
    localStorage.removeItem('SelectedUserId');
    localStorage.removeItem('UserToken');
    localStorage.removeItem('Username');
    localStorage.removeItem('UserName');
    localStorage.removeItem('UserProfileImage');
    localStorage.removeItem('UserStatus');
    localStorage.removeItem('IsAdmin');

    // Get main user from storage
    const mainUser = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
    if (mainUser) {
      setActiveUser({
        ...mainUser,
        isSwitchedUser: false
      });
      
      // Trigger data refresh
      setUserSwitchTrigger(prev => prev + 1);
    }
  }, []);

  // Get current user token for API calls
  const getCurrentUserToken = useCallback(() => {
    return activeUser?.token || fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  }, [activeUser]);

  // Get current user ID for API calls
  const getCurrentUserId = useCallback(() => {
    return activeUser?.user_id || activeUser?.id || fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.user_id;
  }, [activeUser]);

  // Check if current user is switched user
  const isSwitchedUser = useCallback(() => {
    return activeUser?.isSwitchedUser || false;
  }, [activeUser]);

  // Refresh current user data
  const refreshUserData = useCallback(async () => {
    if (!activeUser) return;

    try {
      setIsUserLoading(true);
      // If it's a switched user, we might need to fetch updated data
      // For now, we'll just trigger a refresh
      setUserSwitchTrigger(prev => prev + 1);
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setIsUserLoading(false);
    }
  }, [activeUser]);

  const value = {
    // Current user state
    activeUser,
    isUserLoading,
    userSwitchTrigger, // This will increment when user is switched, components can listen to this
    
    // Available users
    availableUsers,
    loadingUsers,
    fetchAvailableUsers,
    
    // User switching functions
    switchUser,
    switchToMainUser,
    isSwitchedUser,
    
    // Utility functions
    getCurrentUserToken,
    getCurrentUserId,
    refreshUserData,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export default UserContext;
