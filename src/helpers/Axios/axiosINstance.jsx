import axios from "axios";
import { clearStorage, fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

let isRedirecting = false;
const apiInstance = axios.create({
  baseURL: "https://api.flowkar.com/api",
  withCredentials: true,
});

apiInstance.interceptors.request.use((config) => {
  // Check if user has been switched
  const selectedUserToken = localStorage.getItem("UserToken");
  const selectedUserId = localStorage.getItem("SelectedUserId");

  // Use switched user's token and ID if available, otherwise use main user's
  const token =
    selectedUserToken ||
    fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const user =
    selectedUserId ||
    localStorage.getItem("UserId") ||
    fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.user_id;

  const clonedConfig = config;
  if (token) {
    clonedConfig.headers = {
      Authorization: `Bearer ${token}`,
      user: user,
      // brand : 1,
      ...clonedConfig.headers,
      "Content-Type":
        clonedConfig.headers["Content-Type"] || "multipart/form-data",
    };
  } else {
    clonedConfig.headers = {
      "Content-Type": "multipart/form-data",
      ...clonedConfig.headers,
    };
  }
  return clonedConfig;
});

apiInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;
      if (status >= 500 && status <= 599) {
        console.error(`Server Error ${status}: ${error.response.statusText}`);
        window.location.href = "/bad-gateway";
      } else if (status === 403) {
        console.error("Forbidden: Unauthorized access");
        window.location.href = "/PageNotFound";
        clearStorage();
      } else if (status === 400) {
        console.error("Error:", error.response.data.message);
      }
    }
    return Promise.reject(error.response?.data ? error.response.data : error);
  }
);

export default apiInstance;
